# Responsive Auth Pages - Testing Guide

## Quick Test URLs
- Login Page: http://localhost:3001/login
- Register Page: http://localhost:3001/register

## Testing Checklist

### Desktop Testing (1024px+)
**Login Page:**
- [ ] FormCard displays at 450px max-width with proper centering
- [ ] Logo text displays at 2.25rem font-size
- [ ] RotatingText shows at medium size (text-md)
- [ ] Form inputs have full padding (0.75rem)
- [ ] Background squares animate at full size (40px)
- [ ] All elements have proper spacing and alignment

**Register Page:**
- [ ] FormCard displays at 450px max-width
- [ ] FormRow displays side-by-side for name fields
- [ ] All form elements properly spaced
- [ ] Scrollable content works smoothly
- [ ] Logo and branding display correctly

### Tablet Testing (768px-1023px)
**Both Pages:**
- [ ] FormCard reduces to 400px max-width
- [ ] Padding reduces to 2rem
- [ ] Logo font-size reduces to 2rem
- [ ] Input padding reduces to 0.625rem
- [ ] Background squares reduce to ~30px
- [ ] Touch targets remain accessible

### Mobile Testing (481px-767px)
**Both Pages:**
- [ ] FormCard becomes full-width with margins
- [ ] Padding reduces to 1.5rem
- [ ] Logo centers and reduces to 1.75rem
- [ ] RotatingText shows at small size (text-xs)
- [ ] Input fields adapt to mobile sizing
- [ ] FormRow stacks vertically on register page
- [ ] Background squares reduce to ~25px

### Small Mobile Testing (361px-480px)
**Both Pages:**
- [ ] Padding reduces to 1.25rem
- [ ] Logo font-size reduces to 1.5rem
- [ ] Input padding reduces to 0.5rem
- [ ] Icons position correctly (6px from left)
- [ ] Background squares reduce to ~20px
- [ ] All content remains accessible

### Very Small Mobile Testing (≤360px)
**Both Pages:**
- [ ] Minimum padding maintained (1rem)
- [ ] Logo remains readable
- [ ] Input fields remain usable
- [ ] Icons position at 4px from left
- [ ] Background squares at minimum size (~15px)
- [ ] Touch targets remain functional

## Browser Testing
Test in the following browsers:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile Safari (iOS)
- [ ] Chrome Mobile (Android)

## Device Testing
Test on actual devices if possible:
- [ ] iPhone SE (375px width)
- [ ] iPhone 12/13/14 (390px width)
- [ ] iPhone 12/13/14 Pro Max (428px width)
- [ ] iPad (768px width)
- [ ] iPad Pro (1024px width)
- [ ] Android phones (various sizes)

## Performance Testing
- [ ] Smooth animations on all devices
- [ ] No layout shifts during resize
- [ ] Fast loading on mobile networks
- [ ] Responsive images load appropriately

## Accessibility Testing
- [ ] Touch targets are at least 44px
- [ ] Text remains readable at all sizes
- [ ] Form labels are properly associated
- [ ] Focus indicators work on all devices
- [ ] Screen reader compatibility maintained

## Common Issues to Check
- [ ] No horizontal scrolling on any screen size
- [ ] Text doesn't become too small to read
- [ ] Buttons remain clickable/tappable
- [ ] Form validation messages display properly
- [ ] Background animation doesn't impact performance
- [ ] Logo and branding remain recognizable

## Browser Developer Tools Testing
1. Open Chrome DevTools
2. Toggle device toolbar (Ctrl+Shift+M)
3. Test various device presets:
   - iPhone SE
   - iPhone 12 Pro
   - iPad
   - iPad Pro
   - Galaxy S20 Ultra
   - Surface Pro 7
4. Test custom dimensions:
   - 320px (very small mobile)
   - 375px (iPhone SE)
   - 768px (tablet portrait)
   - 1024px (tablet landscape/small desktop)
   - 1440px (desktop)

## Manual Resize Testing
1. Open pages in desktop browser
2. Slowly resize browser window from wide to narrow
3. Check for:
   - Smooth transitions between breakpoints
   - No broken layouts
   - Proper element repositioning
   - Maintained functionality

## Expected Behavior Summary
- **Desktop**: Full-featured layout with optimal spacing
- **Tablet**: Slightly condensed but fully functional
- **Mobile**: Single-column layout with touch-optimized elements
- **Small Mobile**: Minimal but usable interface
- **Very Small**: Essential functionality preserved

## Performance Expectations
- Page load: <3 seconds on 3G
- Animation: 60fps on modern devices
- Interaction: <100ms response time
- Memory: Efficient background animation
